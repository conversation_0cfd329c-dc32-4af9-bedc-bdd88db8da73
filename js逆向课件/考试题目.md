### 1、（单选题，分值 2）AES加密算法中，密钥长度可以是多少位？

- A. 192位
- B. 128位
- C. 256位
- D. 所有上述

  

### 2、（单选题，分值 2）为什么有些网站会使用反爬虫机制,其中错误的是？

- A. 保护数据安全
- B. 提高搜索引擎排名
- C. 防止非法数据采集
- D. 减少服务器负载

  

### 3、（单选题，分值 2）以下哪种JavaScript混淆技术用于将可读的代码变成难以理解的形式？

- A. Minification
- B. Obfuscation
- C. Compression
- D. Encryption

  

### 4、（单选题，分值 2）在Python中，用于解析HTML文档的库是什么？

- A. requests
- B. json
- C. beautifulsoup
- D. selenium

  

### 5、（单选题，分值 2）通过查看JavaScript脚本文件，如何发现可能的混淆或压缩技术？

- A. 查找代码中的注释
- B. 检查变量和函数名是否经过简短化
- C. 查看是否存在大量的空格和换行符
- D. 查找是否使用了console.log输出调试信息

  

### 6、（单选题，分值 2）Ajax的主要特点是什么？

- A. 同步请求和响应
- B. 异步请求和响应
- C. 只支持XML格式的数据传输
- D. 只能在浏览器端使用

  

### 7、（单选题，分值 2）如何通过Elements面板修改网页中的文本内容？

- A. 在Elements面板中直接双击文本进行编辑
- B. 在Console中使用document.editText(element, newText)命令
- C. 在Sources面板中找到文本所在的文件进行修改
- D. 在Network面板中找到文本所在的请求进行修改

  

### 8、（单选题，分值 2）非对称加密算法相对于对称加密算法的主要优势是什么？

- A. 加解密速度快
- B. 密钥管理更简单
- C. 适用于大量数据加密
- D. 安全性更高

  

### 9、（单选题，分值 2）什么是OCR（光学字符识别）技术？

- A. 一种用于网络通信的加密技术
- B. 一种通过图像识别字符的技术
- C. 一种用于加速计算的并行计算技术
- D. 一种用于生成随机数的算法

  

### 10、（单选题，分值 2）User-Agent检测：什么是User-Agent检测？

- A. 检测用户的身份编号
- B. 检测用户浏览器的标识
- C. 检测用户的物理位置
- D. 检测用户的登录状态



### 11、（单选题，分值 2）在JavaScript中，常见的加密算法包括哪些？

- A. MD5
- B. SHA-256
- C. AES
- D. 所有上述

  

### 12、（单选题，分值 2）如何通过JavaScript读取和修改Cookie？

- A. 使用document.cookie属性
- B. 使用localStorage对象
- C. 使用cookie()函数
- D. 使用readCookie()和writeCookie()函数

  

### 13、（单选题，分值 2）在Node.js环境中，全局对象是什么？

- A. document
- B. window
- C. global
- D. root

  

### 14、（单选题，分值 2）在Web浏览器中，访问一个使用TLS保护的网站时，URL的协议前缀是什么？

- A. HTTP
- B. HTTPS
- C. TCP
- D. SSL

  

### 15、（单选题，分值 2）在RPC中，通信的两端通常分别被称为什么？

- A. 客户端和服务器端
- B. 发送者和接收者
- C. 主机和从机
- D. 控制器和执行器

  

### 16、（单选题，分值 2）robots.txt 是用于什么目的的文件？

- A. 配置网站的主页
- B. 限制搜索引擎爬虫访问的页面
- C. 存储网站的静态资源
- D. 用于网站用户登录验证

  

### 17、（单选题，分值 2）AST遍历是指什么？

- A. 在树上寻找叶子节点
- B. 逐层查找根节点
- C. 遍历整个树结构的每个节点
- D. 仅查找深层节点

  

### 18、（单选题，分值 2）常用于对称加密的算法是？

- A. RSA
- B. AES
- C. MD5
- D. SHA-256

  

### 19、（单选题，分值 2）以下不属于HTTP请求方法的是

- A. GET
- B. DELETE
- C. PAT
- D. POST

  

### 20、（单选题，分值 2）以下哪个方法可以通过Selenium执行JavaScript代码？

- A. execute_script()
- B. send_keys()
- C. find_element_by_xpath()
- D. get_attribute()

  

### 21、（问答题，分值 15）

瑞数处理，使用协议处理瑞数cookie

地址：http://iservice.10010.com/e5/query.html

交付：提供200响应即可

交付：提交各功能核心截图

答案：



### 22、（问答题，分值 15）

公共服务数据采集，打开站点采集数据100条、要求对标题进行去重，字段需要hash处理

地址:https://www.ynjzjgcx.com/dataPub/enterprise

字段：企业名称 信用代码 企业注册地

交付：提交各功能核心截图

答案：





### 23、（问答题，分值 15）

采集本站首页数据top10

地址：https://www.chinaindex.net/ranklist/5

字段：字段不限、存储使用mongodb

交付：提供数据和代码截图即可

说明：禁止使用自动化等技术



答案：





### 24、（问答题，分值 15）

使用网络爬虫技术采集该站点10000条数据，且需要设计data去重算法，使用多线程采集 ,字段不限，mongo存储 ,采集地址：www.oklink.com/zh-cn/btc/tx-list 。交付：提供数据和代码截图即可。说明：禁止使用自动化等技术

答案：


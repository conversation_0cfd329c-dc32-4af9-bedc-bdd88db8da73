import execjs
import requests

js_code = execjs.compile(open("33帧.js", "r", encoding="utf-8").read())


headers = {
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Connection": "keep-alive",
    "Origin": "https://fse.agilestudio.cn",
    "Referer": "https://fse.agilestudio.cn/",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-site",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
    "X-Signature": "b0a4e34ea1a6ab21772fec8efb5aec8b",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\""
}
url = "https://fse-api.agilestudio.cn/api/search"
params = {
    "keyword": "火车呼啸而过",
    "page": "1",
    "limit": "12",
    "_platform": "web",
    "_versioin": "0.2.5",
    "_ts": "1752843334176"
}
dic = js_code.call("d", params)
print(dic)
headers["X-Signature"] = dic["sign"]
params["_ts"] = dic["time"]
response = requests.get(url, headers=headers, params=params)

print(response.text)

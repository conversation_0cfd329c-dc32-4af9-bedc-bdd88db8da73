import execjs

import requests

url = "https://api.qimai.cn/indexV2/getIndexRank"
params = {
    "setting": "0",
    "genre": "5000"
}

res = list(params.values())


with open('./七麦.js','r',encoding='utf-8') as f:
    js_code = f.read()

js_code = execjs.compile(js_code)

analysis = js_code.call('main',res)

params['analysis'] = analysis

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
}

response = requests.get(url,headers=headers,params=params)

print(response.json())



import execjs
import requests


url  = 'https://jzsc.mohurd.gov.cn/APi/webApi/dataservice/query/comp/list'

headers = {
    "Referer": "https://jzsc.mohurd.gov.cn/data/company",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
    "timeout": "30000",
    "v": "231012"
}
params = {
    'pg': 0,
    'pgsz': 15,
    'total': 450
}

response = requests.get(url, headers=headers, params=params)

print(response.text)
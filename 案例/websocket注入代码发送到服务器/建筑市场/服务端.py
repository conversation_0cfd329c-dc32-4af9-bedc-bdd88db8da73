import execjs
import requests
import websockets
import asyncio

url = 'https://jzsc.mohurd.gov.cn/APi/webApi/dataservice/query/comp/list'

headers = {
    "Referer": "https://jzsc.mohurd.gov.cn/data/company",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
    "timeout": "30000",
    "v": "231012"
}
params = {
    'pg': 0,
    'pgsz': 15,
    'total': 450
}

response = requests.get(url, headers=headers, params=params)


# 发送消息
async def echo(websocket,response):
    data = response.text
    # 发送消息（await 会等待数据进入发送缓冲区）
    await websocket.send(data)  # 暂停，直到可以发送


# 接收消息
async def recv_msg(websocket):
    while 1:
        # 接收消息（await 会一直等待，直到收到消息）
        recv_text = await websocket.recv()  # 暂停，直到收到数据
        print(recv_text)



async def main_(websocket):
    await echo(websocket,response)
    await recv_msg(websocket)

# 启动服务器的异步函数
async def start_server():
    print("WebSocket服务器启动中...")
    print("服务器地址: ws://127.0.0.1:8080")

    # 创建并启动WebSocket服务器
    async with websockets.serve(main_, "127.0.0.1", 8080):
        await asyncio.Future()  # 永远运行


# 启动服务器
if __name__ == "__main__":
    asyncio.run(start_server())

!(function () {
    if (window.flag) {
    } else {
        // 创建一个标记用来判断是否创建套接字
        window.flag = true;
        const websocket = new WebSocket('ws://127.0.0.1:8080')
//  接收服务端发送的信息
        websocket.onmessage = function (event) {
            var data = event.data
// 调用js解密
            var res = b(data)
            console.log(res)
// 发送解密数据给服务端
            websocket.send(res)
        }
    }
}())
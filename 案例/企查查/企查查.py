import requests
import execjs

class Qichacha:
    def __init__(self):
        self.headers = {
            "43d930d400d14394164a": "255525a14dd2e23c8e65da34e5dfe1762faf18dc7fd1aa225cfc62be3fd3588de6d00b4dc3e5b648d5b9967308726afc2bbd82ea0b477e0df9d0a2feceb437d3",
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
            "cache-control": "no-cache",
            "content-type": "application/json",
            "origin": "https://www.qcc.com",
            "pragma": "no-cache",
            "priority": "u=1, i",
            "referer": "https://www.qcc.com/web/search?key=%E4%B8%87%E8%BE%BE%E9%9B%86%E5%9B%A2",
            "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
            "x-pid": "ef1955056e9088bd7749db8f84835e9e",
            "x-requested-with": "XMLHttpRequest",
            "cookie":"QCCSESSID=94a9f2a2c479c9dcb95f1674d0; qcc_did=3d64d6bc-5c0d-4fcd-a792-560a6c6dac6b; UM_distinctid=19803df53b8670-07d4ec358a8db9-26011151-e1000-19803df53b91992; tfstk=gpOKCgisccmhUQRdSekgrMrljjugeAYeKH8bZgj3FhK9lErhF9mF2dKWb9jQt2WJXUtx8ameZyJwmENHtv5uw65PNmmmij0e8_5W-yjWGM867agGR3B2FS5PNmmGpI9q7_-QttgHRFgOraVQV3_Q1PsNygwWVM6_fZQVN_t5Ffa1kZq7d7NCCVQPfg15N3TsWaj1VjBY9gUC07ppsJ_4RxgaZ7AOJ9I8ZGFJlCPcpiTCX7iX6wE5cesTN7svtqRDWemLgFfesnQDxfNpDe9XVKts6WBwdFdR3QrI5_6WIKXpBDNRjBbRh_6TP7TOOGW2NsGbXNJ6-LOevzF58BAceiWtP7Wl1IXXhHUruF1CP3W2ZmPFAUT2gKfxw51dFgW2iIhxmojArJgtW8yPdNlyX87I4wgUONIm7hyzUOQNWi0tW8yPdN7OmV-aU8WO7; acw_tc=0a47308f17524598319838260e006ee6db71848e3b43c23c34cdb55bcf6d81; CNZZDATA1254842228=1084955462-1752411624-%7C1752459876"
        }
        self.url = 'https://www.qcc.com/api/search/searchMulti'
        self.js = execjs.compile(open('企查查.js', 'r', encoding='utf-8').read())
    

    def get_data(self):
        for i in range(1, 3):
            e = {
                "searchKey": "万达集团",
                "pageIndex": i,
                "pageSize": 20
            }
            he = self.js.call('get_data', e)
            current_headers = self.headers.copy()
            current_headers[he['key']] = he['value']
            res = requests.post(self.url, headers=current_headers, json=e)
            print(res.json())

if __name__ == '__main__':
    qichacha = Qichacha()
    qichacha.get_data()


import requests
import execjs
import pymongo
import json

import subprocess

class GuanNiao:
    def __init__(self):
        self.con = pymongo.MongoClient()
        self.db = self.con['spiders']['gn']
        self.headers = {
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "Origin": "https://www.birdreport.cn",
        "Pragma": "no-cache",
        "Referer": "https://www.birdreport.cn/",
        "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-site",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        }
        self.url = "https://api.birdreport.cn/front/activity/search"
        self.data = '{"limit":"20","page":"%s"}'

    def get_data(self, page):
        with open('观鸟请求体data.js', 'r', encoding='utf-8')as f:
            js_code = f.read()
        js = execjs.compile(js_code)
        res = js.call('beforeSend', self.data % page)
        self.headers['sign'] = res['sign']
        self.headers['timestamp'] = str(res['timestamp'])
        self.headers['requestId'] = res['requestId']
        response = requests.post(self.url, headers=self.headers, data=res['data'])
        return response.json()

    def parse_data(self, data):
        with open('观鸟响应数据.js', 'r', encoding='utf-8')as f:
            js_code = f.read()
        js = execjs.compile(js_code)
        result = js.call('decode', data['data'])
        for i in json.loads(result):
            item = {}
            item['address'] = i['address']
            item['serialId'] = i['serialId']
            item['startTime'] = i['startTime']
            item['endTime'] = i['endTime']
            print(item)
            self.save_data(item)
    def save_data(self, item):
        self.db.insert_one(item)

    def main(self):
        for i in range(1, 10):
            data = self.get_data(i)
            self.parse_data(data)

if __name__ == '__main__':
    gn = GuanNiao()
    gn.main()

var CryptoJS = require('crypto-js');


function decode(a) {
    var key = 'C8EB5514AF5ADDB94B2207B08C66601C';
    var iv = '55DD79C6F04E1A67';
    var b = CryptoJS.enc.Utf8.parse(key);
    var c = CryptoJS.enc.Utf8.parse(iv);
    var d = CryptoJS.AES.decrypt(a, b, {
        iv: c,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    return d.toString(CryptoJS.enc.Utf8)
    
}

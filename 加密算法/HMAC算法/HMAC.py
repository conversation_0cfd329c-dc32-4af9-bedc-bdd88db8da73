import hmac

def hmac_md5(text,key):
    return hmac.new(key.encode(),text.encode(),digestmod='MD5').hexdigest()


def hmac_sha1(text,key):
    return hmac.new(key.encode(),text.encode(),digestmod='SHA1').hexdigest()

def hmac_sha256(text,key):
    hmac_sha256 = hmac.new(key.encode(),digestmod='SHA256')
    

def hmac_sha512(text,key):
    return hmac.new(key.encode(),text.encode(),digestmod='SHA512').hexdigest()

var crypto = require('crypto-js');

function sha1(str) {
    return crypto.SHA1(str).toString();
}

function sha224(str) {
    return crypto.SHA224(str).toString();
}

function sha256(str) {
    return crypto.SHA256(str).toString();
}

function sha512(str) {
    return crypto.SHA512(str).toString();
}

console.log(sha1('123'));
console.log(sha1('123').length);

console.log(sha224('123'));
console.log(sha224('123').length);

console.log(sha256('123'));
console.log(sha256('123').length);

console.log(sha512('123'));
console.log(sha512('123').length);
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
import base64

def aes_encrypt(data_string):
    """
    {{ AURA: Modify - 模仿DES示例代码的结构和风格重写 }}
    使用AES算法加密数据，模式为ECB。
    """
    # 1. 定义一个16字节（128位）的密钥
    #    AES的密钥长度通常是16, 24, or 32字节
    key = "a-16-byte-secret"  # 必须是16字节长

    # 2. 创建AES密码器，模式为ECB
    #    ECB模式简单，但安全性较低，不推荐用于生产环境
    aes = AES.new(
        key=key.encode('utf-8'),
        mode=AES.MODE_ECB,
    )

    # 3. 对输入字符串进行编码和填充
    #    AES的数据块大小是16字节，所以填充到16的倍数
    raw = pad(data_string.encode('utf-8'), AES.block_size)

    # 4. 执行加密并返回原始的二进制密文
    return aes.encrypt(raw)

if __name__ == '__main__':
    data_to_encrypt = "This is a test for AES!"
    encrypted_data = aes_encrypt(data_to_encrypt)
    
    # 为了方便查看，我们通常将其转换为Base64格式
    base64_data = base64.b64encode(encrypted_data)

    print(f"待加密数据: '{data_to_encrypt}'")
    print(f"原始加密结果 (bytes): {encrypted_data}")
    print(f"加密结果 (Base64): {base64_data.decode('utf-8')}")

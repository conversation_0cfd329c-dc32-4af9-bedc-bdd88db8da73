var crypto = require('crypto-js');


function AES_encrypt(key, iv, data) {
    var key = crypto.enc.Utf8.parse(key);
    var iv = crypto.enc.Utf8.parse(iv);
    var data = crypto.enc.Utf8.parse(data);
    var encrypted = crypto.AES.encrypt(data, key, {
        iv: iv,
        mode: crypto.mode.CBC,
        padding: crypto.pad.Pkcs7
    });
    return encrypted.toString();
}

var data = "I love Python!"       // 待加密对象

var aesKey = "6f726c64f2c2057a"    // 密钥 
var aesIv = "0123456789ABCDEF"    // 初始向量a

e = AES_encrypt(aesKey, aesIv, data)
console.log("加密结果：",e)

function AES_decrypt(key, iv, data) {
    var key = crypto.enc.Utf8.parse(key);
    var iv = crypto.enc.Utf8.parse(iv);
 
    var decrypted = crypto.AES.decrypt(data, key, {
        iv: iv,
        mode: crypto.mode.CBC,
        padding: crypto.pad.Pkcs7
    });
    return decrypted.toString(crypto.enc.Utf8);
}

console.log("解密结果：",AES_decrypt(aesKey, aesIv, e))
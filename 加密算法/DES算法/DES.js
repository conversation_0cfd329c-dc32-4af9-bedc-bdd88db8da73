var crypto = require('crypto-js');


function DES_encrypt(key, iv, data) {
    var key = crypto.enc.Utf8.parse(key);
    var iv = crypto.enc.Utf8.parse(iv);
    var data = crypto.enc.Utf8.parse(data);
    var encrypted = crypto.DES.encrypt(data, key, {
        iv: iv,
        mode: crypto.mode.CBC,
        padding: crypto.pad.Pkcs7
    });
    return encrypted.toString();
}

var data = "I love Python!"       // 待加密对象
var desKey = "6f726c64f2c2057"    // 密钥
var desIv = "0123456789ABCDEF"    // 初始向量

e = DES_encrypt(desKey, desIv, data)
console.log("加密结果：",e)

function DES_decrypt(key, iv, data) {
    var key = crypto.enc.Utf8.parse(key);
    var iv = crypto.enc.Utf8.parse(iv);
 
    var decrypted = crypto.DES.decrypt(data, key, {
        iv: iv,
        mode: crypto.mode.CBC,
        padding: crypto.pad.Pkcs7
    });
    return decrypted.toString(crypto.enc.Utf8);
}

console.log("解密结果：",DES_decrypt(desKey, desIv, e))
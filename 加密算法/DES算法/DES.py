from Crypto.Cipher import DES
from Crypto.Util.Padding import pad
import base64

def des_encrypt(data_string):
    """
    使用DES算法加密数据，模式为ECB。
    """
    # 1. 定义一个8字节（64位）的密钥
    #    DES的密钥长度必须是8字节
    key = "d245a0ba"  # 我截取了您AES密钥的前8个字符

    # 2. 创建DES密码器，模式为ECB
    #    ECB模式简单，但安全性较低，不推荐用于生产环境
    des = DES.new(
        key=key.encode('utf-8'),
        mode=DES.MODE_ECB,
    )

    # 3. 对输入字符串进行编码和填充
    #    DES的数据块大小是8字节，所以填充到8的倍数
    raw = pad(data_string.encode('utf-8'), 8)

    # 4. 执行加密并返回原始的二进制密文
    return des.encrypt(raw)


data_to_encrypt = "I love Python!"
encrypted_data = des_encrypt(data_to_encrypt)
base64_data = base64.b64encode(encrypted_data)

# 加密后的结果是二进制(bytes)的，直接打印可能会显示乱码或不可见字符
# 为了方便查看，我们通常将其转换为十六进制或Base64格式
print(f"待加密数据: '{data_to_encrypt}'")
print(f"原始加密结果 (bytes): {encrypted_data}")
print(f"加密结果 (十六进制): {base64_data.decode('utf-8')}")  

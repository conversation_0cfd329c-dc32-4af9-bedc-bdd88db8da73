import asyncio
import websockets

# 发送
async def echo(websocket):
    text = "hello world"
    # 发送消息（await 会等待数据进入发送缓冲区）
    await websocket.send(text)  # 暂停，直到可以发送

#
async def recv_msg(websocket):
    while 1:
        # 接收消息（await 会一直等待，直到收到消息）
        recv_text = await websocket.recv()  # 暂停，直到收到数据
        print(recv_text)

async  def main_(websocket, path):
    await echo(websocket)
    await recv_msg(websocket)

server = websockets.serve(main_, "127.0.0.1", 8080)

asyncio.get_event_loop().run_until_complete(server)
asyncio.get_event_loop().run_forever()
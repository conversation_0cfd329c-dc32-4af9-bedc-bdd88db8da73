import asyncio
import websockets


# 发送
async def echo(websocket):
    text = "hello world"
    # 发送消息（await 会等待数据进入发送缓冲区）
    await websocket.send(text)  # 暂停，直到可以发送


#
async def recv_msg(websocket):
    while 1:
        # 接收消息（await 会一直等待，直到收到消息）
        recv_text = await websocket.recv()  # 暂停，直到收到数据
        print(recv_text)


async def main_(websocket, path):
    await echo(websocket)
    await recv_msg(websocket)


# 启动服务器的异步函数
async def start_server():
    print("WebSocket服务器启动中...")
    print("服务器地址: ws://127.0.0.1:8080")
    print("按 Ctrl+C 停止服务器")

    # 创建并启动WebSocket服务器
    async with websockets.serve(main_, "127.0.0.1", 8080):
        await asyncio.Future()  # 永远运行

# 启动服务器
if __name__ == "__main__":
    try:
        asyncio.run(start_server())
    except KeyboardInterrupt:
        print("\n服务器已停止")

const parse = require('@babel/parser')
const traverse = require("@babel/traverse").default;
const generator = require("@babel/generator").default;

// 测试代码包含各种编码格式
jscode = `
var a = 0x25;           // 十六进制
var b = 0o123;          // 八进制  
var c = 0b1010;         // 二进制
var d = "\\x68\\x65\\x6c\\x6c\\x6f";  // 十六进制转义字符串
var e = "\\u0048\\u0065\\u006c\\u006c\\u006f";  // Unicode转义
`

let ast = parse.parse(jscode);

console.log('=== 修改前的代码 ===');
console.log(generator(ast).code);

console.log('\n=== 检查 extra 属性 ===');

traverse(ast, {
    'NumericLiteral|StringLiteral'(path) {
        var {node} = path;
        
        console.log(`节点类型: ${node.type}`);
        console.log(`实际值: ${JSON.stringify(node.value)}`);
        console.log(`extra.raw: ${node.extra ? node.extra.raw : 'undefined'}`);
        
        // 检查是否是编码格式
        if (node.extra) {
            if (node.type === 'NumericLiteral' && /^0[obx]/i.test(node.extra.raw)) {
                console.log('✅ 发现数字编码格式，清除 extra');
                node.extra = undefined;
            } else if (node.type === 'StringLiteral' && /\\[ux]/gi.test(node.extra.raw)) {
                console.log('✅ 发现字符串编码格式，清除 extra');
                node.extra = undefined;
            }
        }
        console.log('---');
    }
});

console.log('\n=== 修改后的代码 ===');
console.log(generator(ast).code);

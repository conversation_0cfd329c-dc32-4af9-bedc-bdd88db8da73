// 引入 Babel 相关模块
const parse = require('@babel/parser')              // 解析器：将 JS 代码转换为 AST
const traverse = require("@babel/traverse").default; // 遍历器：遍历和操作 AST 节点
const types = require("@babel/types");              // 类型检查：检查和创建 AST 节点
const generator = require("@babel/generator").default; // 生成器：将 AST 转换回 JS 代码

// 待处理的 JavaScript 代码
// 包含一个简单的函数定义和一个函数调用（使用 Unicode 转义字符）
jscode = `  function xl (arg,arg1){
    return arg + arg1
}
var xx = xl('\u0068\u0065\u006c\u006c\u006f','\u002c\u0041\u0053\u0054')
`

// 将 JavaScript 代码解析为 AST（抽象语法树）
var ast = parse.parse(jscode);

/**
 * 判断 AST 节点是否为字面量（可以静态计算的值）
 * 这个函数递归检查节点及其子节点是否都是字面量
 * @param {Node} node - 要检查的 AST 节点
 * @returns {boolean} - 如果是字面量返回 true，否则返回 false
 */
function isNodeLiteral(node) {
    // 如果是数组，检查数组中的每个元素是否都是字面量
    if (Array.isArray(node)) {
        return node.every(ele => isNodeLiteral(ele));
    }

    // 检查是否是基本字面量（字符串、数字、布尔值等）
    if (types.isLiteral(node)) {
        // 排除 null 值，因为 null 不适合进行计算
        if (node.value == null) {
            return false;
        }
        return true;
    }

    // 检查二元表达式（如 1 + 2, "a" + "b"）
    // 只有当左右两边都是字面量时，整个表达式才能静态计算
    if (types.isBinaryExpression(node)) {
        return isNodeLiteral(node.left) && isNodeLiteral(node.right);
    }

    // 检查一元表达式（如 +42, -10）
    // 只处理正号和负号运算符
    if (types.isUnaryExpression(node, {
        "operator": "-"
    }) || types.isUnaryExpression(node, {
        "operator": "+"
    })) {
        return isNodeLiteral(node.argument);
    }

    // 检查对象表达式（如 {a: 1, b: 2}）
    if (types.isObjectExpression(node)) {
        let {properties} = node;
        // 空对象是字面量
        if (properties.length == 0) {
            return true;
        }
        // 所有属性都必须是字面量
        return properties.every(property => isNodeLiteral(property));
    }

    // 检查数组表达式（如 [1, 2, 3]）
    if (types.isArrayExpression(node)) {
        let {elements} = node;
        // 空数组是字面量
        if (elements.length == 0) {
            return true;
        }
        // 所有元素都必须是字面量
        return elements.every(element => isNodeLiteral(element));
    }

    // 如果不是以上任何类型，则不是字面量
    return false;
}

/**
 * AST 转换器对象
 * 定义了如何处理不同类型的 AST 节点
 */
var transform = {
    /**
     * 处理函数声明节点
     * 目标：将简单的函数调用内联为计算结果
     * @param {NodePath} path - 函数声明节点的路径
     */
    FunctionDeclaration(path) {
        // 解构获取节点信息和父路径
        var {node, parentPath} = path;
        var body = node.body      // 函数体（BlockStatement）
        var id = node.id;         // 函数名标识符（Identifier）

        // 检查函数是否是简单的返回函数（只有一个 return 语句）
        if (!types.isReturnStatement(body.body[0])) {
            return; // 不是简单函数，跳过处理
        }

        // 在父级作用域中获取函数名的绑定信息
        // binding 包含了函数的所有引用信息（定义位置、引用位置、引用次数等）
        var binding = parentPath.scope.getBinding(id.name)

        // 检查函数是否被引用，如果没有被引用且不在全局作用域，则删除
        if (!binding.referenced && !parentPath.isProgram()) {
            path.remove(); // 删除未使用的函数（死代码消除）
            return
        }

        // 获取函数的源代码字符串表示
        var js_code = path.toString()
        //console.log(js_code) // 调试用：输出函数源代码

        // 安全性检查：跳过包含危险操作的函数
        // 这些操作不适合静态计算，可能导致副作用或不确定结果
        if (js_code.includes("try") || js_code.includes("random") || js_code.includes("Date")) {
            return // 跳过处理不安全的函数
        }

        // 将函数代码加载到当前 JavaScript 环境中
        // eval() 将字符串形式的函数定义转换为可执行的函数对象
        eval(js_code)
        // 标记是否可以安全地移除原函数定义
        var can_remove = true;

        // 遍历函数的所有引用位置
        for (const referPath of binding.referencePaths) {
            // referPath 指向每个引用函数名的位置，就是每个有函数name的节点
            var {parentPath, node} = referPath;
            // node: 函数名标识符 (Identifier { name: "xl" })
            // parentPath: 包含该标识符node的父节点

            // 检查这个引用是否是函数调用
            // 条件1: 父节点必须是 CallExpression（函数调用）
            // 条件2: 函数名必须是被调用的函数（callee）
            if (!parentPath.isCallExpression({"callee": node})) {
                can_remove = false; // 不是函数调用，不能内联
                continue;           // 跳过这个引用
            }

            // 获取函数调用的参数列表
            var arguments = parentPath.node.arguments;
            // 检查参数是否都是字面量（可以静态计算）
            if (arguments == 0 || !isNodeLiteral(arguments)) {
                can_remove = false; // 参数不是字面量，不能静态计算
                continue;           // 跳过这个调用
            }
            // 执行函数调用，获取计算结果
            // parentPath.toString() 返回完整的函数调用字符串
            // 例如: "xl('hello', 'world')"
            var value = eval(parentPath.toString());
            //console.log(value) // 调试用：输出计算结果

            // 将函数调用替换为计算结果
            // types.valueToNode() 将 JavaScript 值转换为对应的 AST 节点
            parentPath.replaceWith(types.valueToNode(value));
        }

        // 如果所有引用都成功内联，则删除原函数定义
        can_remove && path.remove();
    }
}

// 使用定义的转换器遍历 AST 并应用转换
traverse(ast, transform)

// 将转换后的 AST 重新生成为 JavaScript 代码
var {code} = generator(ast)

// 输出最终的优化后代码
console.log(code)
const parse = require('@babel/parser')
const traverse = require("@babel/traverse").default;
const types = require("@babel/types");
const generator = require("@babel/generator").default;

jscode = `var d = true ? 1 : 2;`
var ast = parse.parse(jscode);
const _jy = {
    "BinaryExpression|UnaryExpression|ConditionalExpression"(path) {
        //
        if (path.isUnaryExpression({operator: "-"}) || path.isUnaryExpression({operator: "void"})) {
            return;
        }
        //分析当前节点是否可以静态计算
        const {confident, value} = path.evaluate();
        if (!confident) return;
        if (typeof value == 'number' && (!Number.isFinite(value))) {
            return;
        }
        // 替换节点
        path.replaceWith(types.valueToNode(value));
    },
}

traverse(ast, _jy)

let {code} = generator(ast);
console.log(code);


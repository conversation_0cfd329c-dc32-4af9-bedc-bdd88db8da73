const generator = require("@babel/generator").default;
const parse = require('@babel/parser')
const traverse = require('@babel/traverse').default
const types = require('@babel/types')
// JS 转 ast语法树
jscode = `
var arr = '3,4,0,5,1,2'['split'](',')
`
// 转换js代码为ast树结构
let ast = parse.parse(jscode);

traverse(ast, {
    CallExpression(path) {
        let {callee, arguments} = path.node
        let data = callee.object.value
        let func = callee.property.value
        let arg = arguments[0].value
        var res = data[func](arg)
        console.log('res:', res);
        console.log('res 是数组吗?', Array.isArray(res));
        console.log('types.valueToNode(res) 的类型:', types.valueToNode(res).type);

        // ✅ 正确的做法：用 replaceWith
        path.replaceWith(types.valueToNode(res))

        // ❌ 错误的做法（会报错）：
        // path.replaceWithMultiple(types.valueToNode(res))

    }
})

// 将ast还原成JavaScript代码
let {code} = generator(ast);
console.log(code)

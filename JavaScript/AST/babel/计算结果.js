const parse = require('@babel/parser')
const traverse = require("@babel/traverse").default;
const types = require("@babel/types");
const generator = require("@babel/generator").default;

jscode = `navigator["\x75\x73"+"\x65\x72"+"\x41\x67"+"\x65\x6e"+"\x74"]
function _xl(){
\tx = 1 + 2 + 3 +4 + 5 + 6 + 7
}
`

var ast = parse.parse(jscode);

var visitor = {
    'BinaryExpression'(path) {
        // console.log(path.evaluate())
        const {confident, value} = path.evaluate();
        //console.log(confident, value);

        path.replaceWith(types.valueToNode(value));
    }
}

traverse(ast, visitor)
var {code} = generator(ast)
console.log(code)
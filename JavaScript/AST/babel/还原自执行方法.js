const generator = require("@babel/generator").default;
const parse = require('@babel/parser')
const traverse = require('@babel/traverse').default
const types = require('@babel/types')


jscode = `!(function () {
    console.log('123')
})
`
// 解析js代码为ast树结构
var ast = parse.parse(jscode);
traverse(ast, {
    UnaryExpression(path) {
        var {argument} = path.node
        if (!types.isFunctionExpression(argument)) {
            return;
        }
        var {id, body, params} = argument
        if (id != null || params.length != 0) {
            return;

        }
        path.replaceWithMultiple(body.body)
    }
})
var {code} = generator(ast)
console.log(code)

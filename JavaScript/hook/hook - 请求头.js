(function () {

    var _setRequestHeader = window.XMLHttpRequest.prototype.setRequestHeader;


    window.XMLHttpRequest.prototype.setRequestHeader = function (key, value) {

        console.log('[hook] setRequestHeader →', key, value);

        if (key === 'Timestamp') {
            debugger;   // 触发断点，方便在 DevTools 中查看调用栈
            // 可以在此修改 value，例如：
            // value = transformTimestamp(value);
        }

        // 保持原有功能：调用原始 setRequestHeader
        return _setRequestHeader.apply(this, arguments);
    };
})();
(function () {
    let cookie_data = document.cookie;              // 用 let 声明
    Object.defineProperty(document, 'cookie', {     // 属性名加引号
        set: function (val) {
            if (val.indexOf('v') !== -1) {
                debugger;
            }
            console.log("这是我设置的cookie",val);
            cookie_data = val;
        },
        get: function () {
            return cookie_data;
        }
    });
})();
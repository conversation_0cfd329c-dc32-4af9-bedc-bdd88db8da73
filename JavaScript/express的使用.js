
// 导入模块
const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } = require('crypto');
var express = require('express')

// 创建web服务器
var app = express();
// get请求
app.get('/user',function(req,res){
    res.send('hello world')
})

app.get('/',function(req,res){
    res.send('这个是首页')
})

app.get('/user',function(req,res){
    res.send('这个是用户页')
})

// 启动web服务器
app.listen(8080,function(){
    console.log('web server running at http://127.0.0.1:8080')
})

//post请求
// 指定参数类型
app.use(express.json())
app.use(express.urlencoded({extended:false}))

app.post('/api',function(req,res){
    console.log(req.body)
    res.send('这个是post请求')
})



